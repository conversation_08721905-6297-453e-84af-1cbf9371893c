import React from 'react';
import '../components/NavBar.css';

const NavBar = ({ showContact = true }) => {
  return (
    <header className="navbar">
      {/* Logo with Blood Drop Icon */}
      <div className="logo-container">
        <div className="blood-drop-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2C12 2 6 8 6 14C6 17.31 8.69 20 12 20C15.31 20 18 17.31 18 14C18 8 12 2 12 2Z" fill="#ff4757"/>
          </svg>
        </div>
        <h1 className="logo-text">
          <span className="vital">VITAL</span>
          <span className="drop">DROP</span>
        </h1>
      </div>

      {/* Navigation Menu */}
      <nav className="navigation">
        <ul className="nav-links">
          <li><a href="/">Home</a></li>
          <li><a href="/about">About</a></li>
          <li><a href="/donate">Donate</a></li>
          <li><a href="/schedule">Schedule</a></li>
          <li><a href="/blood-banks">Find Blood Banks</a></li>
          <li><a href="/notifications">🔔</a></li>
          <li><a href="/login">Login</a></li>
          <li><a href="/signup" style={{
            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '8px',
            textDecoration: 'none'
          }}>Sign Up</a></li>
          <li><a href="/admin" style={{
            background: 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '8px',
            textDecoration: 'none'
          }}>Admin</a></li>
          {showContact && <li><a href="/contact">Contact</a></li>}
        </ul>
      </nav>
    </header>
  );
};

export default NavBar;